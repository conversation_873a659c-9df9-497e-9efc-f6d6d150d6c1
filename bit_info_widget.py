"""
BIT Information Display Widget
Displays Built-In Test information from RF module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QGroupBox, QGridLayout, QApplication)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPalette, QColor
from ui_styles import ModernStyles


class BitInfoWidget(QWidget):
    """Widget for displaying BIT (Built-In Test) information"""
    
    # Signal emitted when query button is clicked
    query_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
        # Auto-refresh timer
        self.auto_refresh_timer = QTimer()
        self.auto_refresh_timer.timeout.connect(self.on_query_clicked)
        
    def init_ui(self):
        """Initialize the user interface"""
        # Main layout
        layout = QVBoxLayout()
        layout.setSpacing(8)
        
        # Create BIT info group box with modern styling
        bit_group = QGroupBox("BIT信息监控")
        bit_group.setStyleSheet(ModernStyles.get_group_box_style())
        
        # Group box layout
        group_layout = QVBoxLayout()
        group_layout.setSpacing(8)
        
        # Control panel with query button and auto-refresh
        control_layout = QHBoxLayout()
        control_layout.setSpacing(6)
        
        # Query button with modern styling
        self.query_button = QPushButton("🔍 查询BIT信息")
        self.query_button.setMinimumHeight(40)
        self.query_button.clicked.connect(self.on_query_clicked)
        self.query_button.setStyleSheet(ModernStyles.get_button_style("success"))
        control_layout.addWidget(self.query_button)
        
        # Auto refresh controls with modern styling
        from PyQt5.QtWidgets import QCheckBox, QSpinBox
        self.auto_refresh_check = QCheckBox("🔄 自动刷新")
        self.auto_refresh_check.setStyleSheet(ModernStyles.get_checkbox_style())
        control_layout.addWidget(self.auto_refresh_check)
        self.auto_refresh_check.toggled.connect(self.on_auto_refresh_toggled)

        # Refresh interval
        control_layout.addWidget(QLabel("间隔(秒):"))
        self.refresh_interval_spin = QSpinBox()
        self.refresh_interval_spin.setRange(1, 60)
        self.refresh_interval_spin.setValue(5)
        self.refresh_interval_spin.setStyleSheet(ModernStyles.get_input_style())
        self.refresh_interval_spin.valueChanged.connect(self.on_interval_changed)
        control_layout.addWidget(self.refresh_interval_spin)
        
        control_layout.addStretch()
        group_layout.addLayout(control_layout)
        
        # BIT information display grid with modern card design
        info_grid = QGridLayout()
        info_grid.setSpacing(12)
        info_grid.setContentsMargins(10, 10, 10, 10)
        
        # Create display labels
        self.info_labels = {}
        
        # Temperature with icon
        info_grid.addWidget(self.create_info_label("🌡️ 温度:"), 0, 0)
        self.info_labels['temperature'] = self.create_value_label()
        info_grid.addWidget(self.info_labels['temperature'], 0, 1)
        info_grid.addWidget(self.create_unit_label("°C"), 0, 2)

        # Voltage with icon
        info_grid.addWidget(self.create_info_label("⚡ 输入电压:"), 0, 3)
        self.info_labels['voltage'] = self.create_value_label()
        info_grid.addWidget(self.info_labels['voltage'], 0, 4)
        info_grid.addWidget(self.create_unit_label("V"), 0, 5)

        # Current with icon
        info_grid.addWidget(self.create_info_label("🔌 电流:"), 1, 0)
        self.info_labels['current'] = self.create_value_label()
        info_grid.addWidget(self.info_labels['current'], 1, 1)
        info_grid.addWidget(self.create_unit_label("A"), 1, 2)

        # Power with icon
        info_grid.addWidget(self.create_info_label("💡 功率:"), 1, 3)
        self.info_labels['power'] = self.create_value_label()
        info_grid.addWidget(self.info_labels['power'], 1, 4)
        info_grid.addWidget(self.create_unit_label("W"), 1, 5)

        # Status with icon
        info_grid.addWidget(self.create_info_label("📊 状态:"), 2, 0)
        self.info_labels['status'] = self.create_status_label()
        self.info_labels['status'].setText("未查询")
        info_grid.addWidget(self.info_labels['status'], 2, 1, 1, 5)
        
        # Set column stretch
        for i in range(6):
            info_grid.setColumnStretch(i, 1)
            
        group_layout.addLayout(info_grid)
        bit_group.setLayout(group_layout)
        
        layout.addWidget(bit_group)
        self.setLayout(layout)
        
    def create_info_label(self, text):
        """Create a label for information display with modern styling"""
        label = QLabel(text)
        label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        font = QFont()
        font.setPointSize(10)
        font.setWeight(QFont.Medium)
        label.setFont(font)
        label.setStyleSheet(f"color: {ModernStyles.TEXT_PRIMARY}; padding: 4px;")
        return label

    def create_value_label(self):
        """Create a label for value display with modern styling"""
        label = QLabel("--")
        label.setAlignment(Qt.AlignCenter)
        label.setMinimumWidth(64)
        label.setMinimumHeight(26)
        font = QFont()
        font.setPointSize(12)
        font.setBold(True)
        label.setFont(font)
        label.setStyleSheet(f"""
            QLabel {{
                color: {ModernStyles.PRIMARY_COLOR};
                background-color: {ModernStyles.PRIMARY_LIGHT};
                border-radius: 4px;
                padding: 6px 12px;
            }}
        """)
        return label

    def create_unit_label(self, text):
        """Create a unit label with modern styling"""
        label = QLabel(text)
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        font = QFont()
        font.setPointSize(9)
        label.setFont(font)
        label.setStyleSheet(f"color: {ModernStyles.TEXT_SECONDARY}; padding: 4px;")
        return label

    def create_status_label(self):
        """Create a status label with modern styling"""
        label = QLabel("--")
        label.setAlignment(Qt.AlignCenter)
        label.setMinimumHeight(26)
        font = QFont()
        font.setPointSize(11)
        font.setBold(True)
        label.setFont(font)
        label.setStyleSheet(f"""
            QLabel {{
                color: {ModernStyles.TEXT_WHITE};
                background-color: {ModernStyles.TEXT_SECONDARY};
                border-radius: 4px;
                padding: 6px 12px;
            }}
        """)
        return label
        
    def on_query_clicked(self):
        """Handle query button click"""
        self.info_labels['status'].setText("查询中...")
        self.info_labels['status'].setStyleSheet("color: #FFA500;")
        self.query_requested.emit()
        
    def on_auto_refresh_toggled(self, checked):
        """Handle auto refresh toggle"""
        if checked:
            interval = self.refresh_interval_spin.value() * 1000  # Convert to ms
            self.auto_refresh_timer.start(interval)
            self.on_query_clicked()  # Query immediately
        else:
            self.auto_refresh_timer.stop()
            
    def on_interval_changed(self, value):
        """Handle refresh interval change"""
        if self.auto_refresh_check.isChecked():
            self.auto_refresh_timer.setInterval(value * 1000)
            
    def update_bit_info(self, bit_info):
        """Update BIT information display
        
        Args:
            bit_info: dict with keys: temperature, voltage, current, power
        """
        if bit_info is None:
            self.info_labels['status'].setText("❌ 查询失败")
            self.info_labels['status'].setStyleSheet(f"""
                QLabel {{
                    color: {ModernStyles.TEXT_WHITE};
                    background-color: {ModernStyles.ERROR_COLOR};
                    border-radius: 4px;
                    padding: 6px 12px;
                }}
            """)
            for key in ['temperature', 'voltage', 'current', 'power']:
                self.info_labels[key].setText("--")
            return

        # Update values with modern styling
        self.info_labels['temperature'].setText(f"{bit_info['temperature']:.0f}")
        self.info_labels['voltage'].setText(f"{bit_info['voltage']:.1f}")
        self.info_labels['current'].setText(f"{bit_info['current']:.3f}")
        self.info_labels['power'].setText(f"{bit_info['power']:.2f}")

        # Update status with success styling
        self.info_labels['status'].setText("✅ 正常")
        self.info_labels['status'].setStyleSheet(f"""
            QLabel {{
                color: {ModernStyles.TEXT_WHITE};
                background-color: {ModernStyles.SUCCESS_COLOR};
                border-radius: 4px;
                padding: 6px 12px;
            }}
        """)

        # Force UI update
        QApplication.processEvents()

        # Check for temperature warnings with color coding
        if bit_info['temperature'] > 80:
            self.info_labels['temperature'].setStyleSheet(f"""
                QLabel {{
                    color: {ModernStyles.TEXT_WHITE};
                    background-color: {ModernStyles.ERROR_COLOR};
                    border-radius: 4px;
                    padding: 6px 12px;
                }}
            """)
        elif bit_info['temperature'] > 60:
            self.info_labels['temperature'].setStyleSheet(f"""
                QLabel {{
                    color: {ModernStyles.TEXT_WHITE};
                    background-color: {ModernStyles.WARNING_COLOR};
                    border-radius: 4px;
                    padding: 6px 12px;
                }}
            """)
        else:
            self.info_labels['temperature'].setStyleSheet(f"""
                QLabel {{
                    color: {ModernStyles.PRIMARY_COLOR};
                    background-color: {ModernStyles.PRIMARY_LIGHT};
                    border-radius: 4px;
                    padding: 6px 12px;
                }}
            """)
            
    def clear_display(self):
        """Clear all displayed values with modern styling"""
        for key in ['temperature', 'voltage', 'current', 'power']:
            self.info_labels[key].setText("--")
            self.info_labels[key].setStyleSheet(f"""
                QLabel {{
                    color: {ModernStyles.TEXT_SECONDARY};
                    background-color: {ModernStyles.BACKGROUND_PRIMARY};
                    border-radius: 4px;
                    padding: 6px 12px;
                }}
            """)

        self.info_labels['status'].setText("🔌 未连接")
        self.info_labels['status'].setStyleSheet(f"""
            QLabel {{
                color: {ModernStyles.TEXT_WHITE};
                background-color: {ModernStyles.TEXT_SECONDARY};
                border-radius: 4px;
                padding: 6px 12px;
            }}
        """)

        # Stop auto refresh
        if self.auto_refresh_check.isChecked():
            self.auto_refresh_check.setChecked(False)