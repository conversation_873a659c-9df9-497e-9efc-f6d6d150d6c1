"""
UI Demo Script - Shows the improved interface features
"""

import sys
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer
from main_window_v2 import MainWindow


class UIDemo:
    """Demo class to showcase UI improvements"""
    
    def __init__(self, main_window):
        self.window = main_window
        self.demo_timer = QTimer()
        self.demo_step = 0
        self.demo_timer.timeout.connect(self.next_demo_step)
        
    def start_demo(self):
        """Start the UI demo"""
        reply = QMessageBox.question(
            self.window, 
            "UI演示", 
            "是否开始UI功能演示？\n这将展示新的界面特性和改进。",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.demo_timer.start(2000)  # 2 seconds between steps
            self.next_demo_step()
    
    def next_demo_step(self):
        """Execute next demo step"""
        if self.demo_step == 0:
            self.window.log_to_status("🎨 欢迎使用新的现代化UI界面！")
            self.window.log_to_status("✨ 界面采用了全新的设计语言")
            
        elif self.demo_step == 1:
            self.window.log_to_status("🎯 主要改进包括：")
            self.window.log_to_status("• 现代化的颜色方案和圆角设计")
            self.window.log_to_status("• 更好的视觉层次和间距")
            
        elif self.demo_step == 2:
            self.window.log_to_status("🔧 功能改进：")
            self.window.log_to_status("• 增强的状态指示器")
            self.window.log_to_status("• 自动滚动控制")
            self.window.log_to_status("• 更直观的按钮和控件")
            
        elif self.demo_step == 3:
            self.window.log_to_status("📊 BIT信息显示优化：")
            self.window.log_to_status("• 彩色状态指示")
            self.window.log_to_status("• 图标化信息展示")
            self.window.log_to_status("• 温度警告颜色编码")
            
        elif self.demo_step == 4:
            self.window.log_to_status("🎮 控制界面改进：")
            self.window.log_to_status("• 更大的触控区域")
            self.window.log_to_status("• 清晰的视觉反馈")
            self.window.log_to_status("• 统一的设计风格")
            
        elif self.demo_step == 5:
            self.window.log_to_status("🚀 演示完成！")
            self.window.log_to_status("现在您可以开始使用新的界面了")
            self.demo_timer.stop()
            return
            
        self.demo_step += 1


def main():
    """Main demo entry point"""
    app = QApplication(sys.argv)
    
    # Create main window
    window = MainWindow()
    window.show()
    
    # Create and start demo
    demo = UIDemo(window)
    
    # Start demo after a short delay
    QTimer.singleShot(1000, demo.start_demo)
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
