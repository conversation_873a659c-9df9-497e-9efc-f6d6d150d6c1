"""
Dual Channel Control Widget for RF Module Test - V2.0
Provides UI controls for 2 independent RF channels with different frequency ranges
"""

from PyQt5.QtWidgets import (QGroupBox, QGridLayout, QLabel, QDoubleSpinBox,
                             QComboBox, QSpinBox, QCheckBox, QPushButton,
                             QHBoxLayout, QVBoxLayout, QToolButton, QFrame)
from PyQt5.QtCore import pyqtSignal, Qt, QTimer
from PyQt5.QtGui import QFont
from ui_styles import ModernStyles


class EnhancedDoubleSpinBox(QDoubleSpinBox):
    """Enhanced QDoubleSpinBox with mouse wheel support"""
    
    wheelChanged = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFocusPolicy(Qt.StrongFocus)
        
    def wheelEvent(self, event):
        """Handle mouse wheel events for fine control"""
        if self.hasFocus():
            delta = event.angleDelta().y()
            
            # Determine step size based on modifiers
            if event.modifiers() & Qt.ControlModifier:
                step = self.singleStep() * 10
            elif event.modifiers() & Qt.ShiftModifier:
                step = self.singleStep() * 0.1
            else:
                step = self.singleStep()
                
            if delta > 0:
                self.setValue(self.value() + step)
            else:
                self.setValue(self.value() - step)
                
            self.wheelChanged.emit()
            event.accept()
        else:
            event.ignore()
            
    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.editingFinished.emit()
            event.accept()
        else:
            super().keyPressEvent(event)


class SingleChannelWidget(QGroupBox):
    """Widget for controlling a single RF channel"""
    
    parameters_changed = pyqtSignal()
    
    def __init__(self, title, channel_num, freq_range, parent=None):
        super().__init__(title, parent)
        self.channel_num = channel_num
        self.freq_min, self.freq_max = freq_range
        self.init_ui()
        
    def init_ui(self):
        """Initialize the UI components with modern styling"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(16)
        
        # Row 1: Signal Enable checkbox
        signal_row = QHBoxLayout()
        self.signal_enable = QCheckBox("信号开启")
        self.signal_enable.setChecked(False)
        self.signal_enable.stateChanged.connect(self.on_parameter_changed)
        signal_row.addWidget(self.signal_enable)
        signal_row.addStretch()
        main_layout.addLayout(signal_row)
        
        # Row 2: Work Mode selection
        mode_row = QHBoxLayout()
        mode_row.addWidget(QLabel("工作模式:"))
        self.work_mode = QComboBox()
        self.work_mode.addItems(["单频标校", "扫频标校(不支持)", "梳状谱标校", "低功耗"])
        self.work_mode.currentTextChanged.connect(self.on_parameter_changed)
        self.work_mode.setMinimumWidth(120)
        mode_row.addWidget(self.work_mode)
        mode_row.addStretch()
        main_layout.addLayout(mode_row)
        
        # Row 3: Frequency control
        freq_row = QHBoxLayout()
        freq_row.addWidget(QLabel("频率 (MHz):"))
        
        self.freq_down_10 = QToolButton()
        self.freq_down_10.setText("-10")
        self.freq_down_10.setMinimumSize(35, 28)
        self.freq_down_10.clicked.connect(lambda: self.adjust_frequency(-10))
        freq_row.addWidget(self.freq_down_10)
        
        self.freq_down_1 = QToolButton()
        self.freq_down_1.setText("-1")
        self.freq_down_1.setMinimumSize(30, 28)
        self.freq_down_1.clicked.connect(lambda: self.adjust_frequency(-1))
        freq_row.addWidget(self.freq_down_1)
        
        self.frequency = EnhancedDoubleSpinBox()
        self.frequency.setRange(self.freq_min, self.freq_max)
        self.frequency.setValue(self.freq_min)
        self.frequency.setDecimals(0)
        self.frequency.setSingleStep(1)
        self.frequency.setSuffix(" MHz")
        self.frequency.setMinimumWidth(140)
        self.frequency.setMinimumHeight(28)
        self.frequency.valueChanged.connect(self.on_parameter_changed)
        self.frequency.wheelChanged.connect(self.on_parameter_changed)
        freq_row.addWidget(self.frequency)
        
        self.freq_up_1 = QToolButton()
        self.freq_up_1.setText("+1")
        self.freq_up_1.setMinimumSize(30, 28)
        self.freq_up_1.clicked.connect(lambda: self.adjust_frequency(1))
        freq_row.addWidget(self.freq_up_1)
        
        self.freq_up_10 = QToolButton()
        self.freq_up_10.setText("+10")
        self.freq_up_10.setMinimumSize(35, 28)
        self.freq_up_10.clicked.connect(lambda: self.adjust_frequency(10))
        freq_row.addWidget(self.freq_up_10)
        
        freq_row.addStretch()
        main_layout.addLayout(freq_row)
        
        # Row 4: Attenuation control
        att_row = QHBoxLayout()
        att_row.addWidget(QLabel("衰减 (dB):"))
        
        self.att_down = QToolButton()
        self.att_down.setText("-5")
        self.att_down.setMinimumSize(30, 28)
        self.att_down.clicked.connect(lambda: self.adjust_attenuation(-5))
        att_row.addWidget(self.att_down)
        
        self.attenuation = QSpinBox()
        self.attenuation.setRange(0, 50)
        self.attenuation.setSingleStep(5)
        self.attenuation.setValue(0)
        self.attenuation.setSuffix(" dB")
        self.attenuation.setMinimumWidth(80)
        self.attenuation.setMinimumHeight(28)
        self.attenuation.valueChanged.connect(self.on_parameter_changed)
        att_row.addWidget(self.attenuation)
        
        self.att_up = QToolButton()
        self.att_up.setText("+5")
        self.att_up.setMinimumSize(30, 28)
        self.att_up.clicked.connect(lambda: self.adjust_attenuation(5))
        att_row.addWidget(self.att_up)
        
        att_row.addStretch()
        main_layout.addLayout(att_row)
        
        self.setLayout(main_layout)
        self.apply_modern_styles()
        
    def adjust_frequency(self, delta):
        """Adjust frequency by delta MHz"""
        new_value = self.frequency.value() + delta
        self.frequency.setValue(new_value)
        
    def adjust_attenuation(self, delta):
        """Adjust attenuation by delta dB"""
        new_value = self.attenuation.value() + delta
        self.attenuation.setValue(new_value)
        
    def on_parameter_changed(self):
        """Emit signal when any parameter changes"""
        self.parameters_changed.emit()
        
    def get_parameters(self):
        """Get current channel parameters"""
        return {
            'signal_enable': 1 if self.signal_enable.isChecked() else 0,
            'work_mode': self.work_mode.currentIndex(),
            'frequency': self.frequency.value(),
            'attenuation': self.attenuation.value()
        }
        
    def set_parameters(self, params):
        """Set channel parameters"""
        self.signal_enable.setChecked(params.get('signal_enable', 0) == 1)
        self.work_mode.setCurrentIndex(params.get('work_mode', 0))
        self.frequency.setValue(params.get('frequency', self.freq_min))
        self.attenuation.setValue(params.get('attenuation', 0))

    def apply_modern_styles(self):
        """Apply modern styling to the channel widget"""
        # Style the group box
        self.setStyleSheet(ModernStyles.get_group_box_style())

        # Style input controls
        self.frequency.setStyleSheet(ModernStyles.get_input_style())
        self.attenuation.setStyleSheet(ModernStyles.get_input_style())
        self.work_mode.setStyleSheet(ModernStyles.get_input_style())

        # Style checkbox
        self.signal_enable.setStyleSheet(ModernStyles.get_checkbox_style())

        # Style tool buttons
        for btn in [self.freq_down_10, self.freq_down_1, self.freq_up_1,
                   self.freq_up_10, self.att_down, self.att_up]:
            btn.setStyleSheet(ModernStyles.get_tool_button_style())


class DualChannelWidget(QFrame):
    """Widget for controlling both RF channels"""
    
    send_requested = pyqtSignal(dict, dict)  # channel1_params, channel2_params
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.auto_send_enabled = True
        self.auto_send_timer = QTimer()
        self.auto_send_timer.timeout.connect(self.auto_send)
        self.auto_send_timer.setSingleShot(True)
        self.init_ui()
        
    def init_ui(self):
        """Initialize the UI components"""
        main_layout = QVBoxLayout()
        
        # Channel controls in vertical layout for better space utilization
        channels_layout = QVBoxLayout()
        channels_layout.setSpacing(15)  # Add space between channels
        
        # Channel 1: 30-3000 MHz (Low band)
        self.channel1 = SingleChannelWidget("通道1 (低段 30-3000 MHz)", 1, (30, 3000))
        self.channel1.parameters_changed.connect(self.on_parameter_changed)
        channels_layout.addWidget(self.channel1)
        
        # Channel 2: 2000-18000 MHz (High band)
        self.channel2 = SingleChannelWidget("通道2 (高段 2-18 GHz)", 2, (2000, 18000))
        self.channel2.parameters_changed.connect(self.on_parameter_changed)
        channels_layout.addWidget(self.channel2)
        
        main_layout.addLayout(channels_layout)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        # Auto send checkbox
        self.auto_send_cb = QCheckBox("自动发送")
        self.auto_send_cb.setChecked(self.auto_send_enabled)
        self.auto_send_cb.stateChanged.connect(self.toggle_auto_send)
        button_layout.addWidget(self.auto_send_cb)
        
        button_layout.addStretch()
        
        # Manual send button
        self.send_btn = QPushButton("发送参数")
        self.send_btn.setFont(QFont("Arial", 10, QFont.Bold))
        self.send_btn.clicked.connect(self.send_parameters)
        self.send_btn.setMinimumHeight(35)
        button_layout.addWidget(self.send_btn)
        
        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)
        self.apply_modern_styles()
        
    def on_parameter_changed(self):
        """Handle parameter changes"""
        if self.auto_send_enabled:
            # Reset the timer for debouncing
            self.auto_send_timer.stop()
            self.auto_send_timer.start(500)  # 500ms delay
            
    def toggle_auto_send(self, state):
        """Toggle auto-send feature"""
        self.auto_send_enabled = state == Qt.Checked
        
    def auto_send(self):
        """Auto-send parameters after delay"""
        if self.auto_send_enabled:
            self.send_parameters()
            
    def send_parameters(self):
        """Send parameters for both channels"""
        ch1_params = self.channel1.get_parameters()
        ch2_params = self.channel2.get_parameters()
        
        # Visual feedback
        self.send_btn.setText("发送中...")
        self.send_btn.setEnabled(False)
        
        # Emit signal with both channel parameters
        self.send_requested.emit(ch1_params, ch2_params)
        
        # Reset button after short delay
        QTimer.singleShot(1000, self.reset_send_button)
        
    def reset_send_button(self):
        """Reset send button appearance"""
        self.send_btn.setText("发送参数")
        self.send_btn.setEnabled(True)
        
    def get_all_parameters(self):
        """Get parameters for both channels"""
        return {
            'channel1': self.channel1.get_parameters(),
            'channel2': self.channel2.get_parameters()
        }
        
    def set_all_parameters(self, params):
        """Set parameters for both channels"""
        if 'channel1' in params:
            self.channel1.set_parameters(params['channel1'])
        if 'channel2' in params:
            self.channel2.set_parameters(params['channel2'])

    def apply_modern_styles(self):
        """Apply modern styling to the dual channel widget"""
        # Style the frame
        self.setStyleSheet(f"""
        QFrame {{
            background-color: {ModernStyles.BACKGROUND_SECONDARY};
            border-radius: 8px;
            padding: 8px;
        }}
        """)

        # Style buttons
        self.send_btn.setStyleSheet(ModernStyles.get_button_style("success"))

        # Style checkbox
        self.auto_send_cb.setStyleSheet(ModernStyles.get_checkbox_style())