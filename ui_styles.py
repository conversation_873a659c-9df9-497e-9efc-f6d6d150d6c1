"""
Modern UI Styles for RF Module Test Application
Provides consistent styling across all components
"""

from PyQt5.QtCore import Qt
from PyQt5.QtGui import <PERSON>F<PERSON>, <PERSON><PERSON><PERSON><PERSON>, QColor


class ModernStyles:
    """Modern UI styling constants and methods"""
    
    # Color Palette
    PRIMARY_COLOR = "#2196F3"      # Blue
    PRIMARY_DARK = "#1976D2"       # Dark Blue
    PRIMARY_LIGHT = "#BBDEFB"      # Light Blue
    
    SECONDARY_COLOR = "#4CAF50"    # Green
    SECONDARY_DARK = "#388E3C"     # Dark Green
    SECONDARY_LIGHT = "#C8E6C9"    # Light Green
    
    ACCENT_COLOR = "#FF9800"       # Orange
    ACCENT_DARK = "#F57C00"        # Dark Orange
    ACCENT_LIGHT = "#FFE0B2"       # Light Orange
    
    ERROR_COLOR = "#F44336"        # Red
    WARNING_COLOR = "#FF9800"      # Orange
    SUCCESS_COLOR = "#4CAF50"      # Green
    INFO_COLOR = "#2196F3"         # Blue
    
    # Background Colors
    BACKGROUND_PRIMARY = "#FAFAFA"    # Very Light Gray
    BACKGROUND_SECONDARY = "#FFFFFF"  # White
    BACKGROUND_DARK = "#37474F"       # Dark Gray
    
    # Text Colors
    TEXT_PRIMARY = "#212121"       # Dark Gray
    TEXT_SECONDARY = "#757575"     # Medium Gray
    TEXT_HINT = "#BDBDBD"         # Light Gray
    TEXT_WHITE = "#FFFFFF"        # White
    
    # Border and Shadow
    BORDER_COLOR = "#E0E0E0"       # Light Gray
    SHADOW_COLOR = "rgba(0, 0, 0, 0.1)"
    
    @staticmethod
    def get_main_window_style():
        """Get main window stylesheet"""
        return f"""
        QMainWindow {{
            background-color: {ModernStyles.BACKGROUND_PRIMARY};
            color: {ModernStyles.TEXT_PRIMARY};
        }}
        
        QWidget {{
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            font-size: 9pt;
        }}
        """
    
    @staticmethod
    def get_group_box_style():
        """Get group box stylesheet"""
        return f"""
        QGroupBox {{
            font-weight: 600;
            font-size: 10pt;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            color: {ModernStyles.TEXT_PRIMARY};
            border: 1px solid {ModernStyles.BORDER_COLOR};
            border-radius: 8px;
            margin-top: 14px;
            padding-top: 10px;
            background-color: {ModernStyles.BACKGROUND_SECONDARY};
        }}

        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 16px;
            padding: 0 10px 0 10px;
            color: {ModernStyles.PRIMARY_COLOR};
            background-color: {ModernStyles.BACKGROUND_SECONDARY};
            font-weight: 600;
        }}
        """
    
    @staticmethod
    def get_button_style(button_type="primary"):
        """Get button stylesheet based on type"""
        if button_type == "primary":
            bg_color = ModernStyles.PRIMARY_COLOR
            bg_hover = ModernStyles.PRIMARY_DARK
            text_color = ModernStyles.TEXT_WHITE
            border_color = ModernStyles.PRIMARY_COLOR
        elif button_type == "success":
            bg_color = ModernStyles.SECONDARY_COLOR
            bg_hover = ModernStyles.SECONDARY_DARK
            text_color = ModernStyles.TEXT_WHITE
            border_color = ModernStyles.SECONDARY_COLOR
        elif button_type == "warning":
            bg_color = ModernStyles.WARNING_COLOR
            bg_hover = ModernStyles.ACCENT_DARK
            text_color = ModernStyles.TEXT_WHITE
            border_color = ModernStyles.WARNING_COLOR
        elif button_type == "danger":
            bg_color = ModernStyles.ERROR_COLOR
            bg_hover = "#D32F2F"
            text_color = ModernStyles.TEXT_WHITE
            border_color = ModernStyles.ERROR_COLOR
        else:  # secondary
            bg_color = ModernStyles.BACKGROUND_SECONDARY
            bg_hover = ModernStyles.BORDER_COLOR
            text_color = ModernStyles.TEXT_PRIMARY
            border_color = ModernStyles.BORDER_COLOR

        return f"""
        QPushButton {{
            background-color: {bg_color};
            color: {text_color};
            border: 1px solid {border_color};
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
            font-size: 9pt;
            min-height: 24px;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }}

        QPushButton:hover {{
            background-color: {bg_hover};
            transform: translateY(-1px);
        }}

        QPushButton:pressed {{
            background-color: {bg_hover};
            transform: translateY(0px);
        }}

        QPushButton:disabled {{
            background-color: {ModernStyles.BORDER_COLOR};
            color: {ModernStyles.TEXT_HINT};
            border-color: {ModernStyles.BORDER_COLOR};
        }}
        """
    
    @staticmethod
    def get_input_style():
        """Get input controls stylesheet"""
        return f"""
        QSpinBox, QDoubleSpinBox, QComboBox {{
            border: 1px solid {ModernStyles.BORDER_COLOR};
            border-radius: 6px;
            padding: 6px 10px;
            background-color: {ModernStyles.BACKGROUND_SECONDARY};
            color: {ModernStyles.TEXT_PRIMARY};
            font-size: 9pt;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            min-height: 24px;
        }}

        QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
            border-color: {ModernStyles.PRIMARY_COLOR};
            border-width: 2px;
            outline: none;
        }}

        QSpinBox:hover, QDoubleSpinBox:hover, QComboBox:hover {{
            border-color: {ModernStyles.PRIMARY_LIGHT};
        }}

        QSpinBox:disabled, QDoubleSpinBox:disabled, QComboBox:disabled {{
            background-color: {ModernStyles.BACKGROUND_PRIMARY};
            color: {ModernStyles.TEXT_HINT};
            border-color: {ModernStyles.BORDER_COLOR};
        }}

        QComboBox::drop-down {{
            border: none;
            width: 24px;
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
        }}

        QComboBox::down-arrow {{
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid {ModernStyles.TEXT_SECONDARY};
            margin-right: 6px;
        }}

        QComboBox QAbstractItemView {{
            border: 1px solid {ModernStyles.BORDER_COLOR};
            border-radius: 6px;
            background-color: {ModernStyles.BACKGROUND_SECONDARY};
            selection-background-color: {ModernStyles.PRIMARY_LIGHT};
            selection-color: {ModernStyles.TEXT_PRIMARY};
        }}
        """
    
    @staticmethod
    def get_checkbox_style():
        """Get checkbox stylesheet"""
        return f"""
        QCheckBox {{
            color: {ModernStyles.TEXT_PRIMARY};
            font-size: 9pt;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            font-weight: 500;
            spacing: 10px;
        }}

        QCheckBox::indicator {{
            width: 18px;
            height: 18px;
            border: 1px solid {ModernStyles.BORDER_COLOR};
            border-radius: 4px;
            background-color: {ModernStyles.BACKGROUND_SECONDARY};
        }}

        QCheckBox::indicator:hover {{
            border-color: {ModernStyles.PRIMARY_COLOR};
            border-width: 2px;
        }}

        QCheckBox::indicator:checked {{
            background-color: {ModernStyles.PRIMARY_COLOR};
            border-color: {ModernStyles.PRIMARY_COLOR};
            image: none;
        }}

        QCheckBox::indicator:checked:hover {{
            background-color: {ModernStyles.PRIMARY_DARK};
            border-color: {ModernStyles.PRIMARY_DARK};
        }}

        QCheckBox::indicator:disabled {{
            background-color: {ModernStyles.BACKGROUND_PRIMARY};
            border-color: {ModernStyles.BORDER_COLOR};
        }}

        QCheckBox:disabled {{
            color: {ModernStyles.TEXT_HINT};
        }}
        """
    
    @staticmethod
    def get_text_edit_style():
        """Get text edit stylesheet"""
        return f"""
        QTextEdit {{
            border: 1px solid {ModernStyles.BORDER_COLOR};
            border-radius: 6px;
            background-color: {ModernStyles.BACKGROUND_SECONDARY};
            color: {ModernStyles.TEXT_PRIMARY};
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 8pt;
            padding: 10px;
            selection-background-color: {ModernStyles.PRIMARY_LIGHT};
            selection-color: {ModernStyles.TEXT_PRIMARY};
        }}

        QTextEdit:focus {{
            border-color: {ModernStyles.PRIMARY_COLOR};
            border-width: 2px;
            outline: none;
        }}

        QScrollBar:vertical {{
            background-color: {ModernStyles.BACKGROUND_PRIMARY};
            width: 12px;
            border-radius: 6px;
        }}

        QScrollBar::handle:vertical {{
            background-color: {ModernStyles.BORDER_COLOR};
            border-radius: 6px;
            min-height: 20px;
        }}

        QScrollBar::handle:vertical:hover {{
            background-color: {ModernStyles.TEXT_SECONDARY};
        }}
        """

    @staticmethod
    def get_label_style(label_type="normal"):
        """Get label stylesheet based on type"""
        if label_type == "title":
            return f"""
            QLabel {{
                color: {ModernStyles.TEXT_PRIMARY};
                font-size: 11pt;
                font-weight: 600;
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                padding: 4px 0px;
            }}
            """
        elif label_type == "value":
            return f"""
            QLabel {{
                color: {ModernStyles.PRIMARY_COLOR};
                font-size: 10pt;
                font-weight: 500;
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                background-color: {ModernStyles.PRIMARY_LIGHT};
                border-radius: 4px;
                padding: 6px 12px;
                border: 1px solid {ModernStyles.PRIMARY_COLOR};
            }}
            """
        elif label_type == "unit":
            return f"""
            QLabel {{
                color: {ModernStyles.TEXT_SECONDARY};
                font-size: 9pt;
                font-weight: 400;
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                padding: 4px 2px;
            }}
            """
        else:  # normal
            return f"""
            QLabel {{
                color: {ModernStyles.TEXT_PRIMARY};
                font-size: 9pt;
                font-weight: 500;
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                padding: 4px 6px;
            }}
            """

    @staticmethod
    def get_scroll_area_style():
        """Get scroll area stylesheet"""
        return f"""
        QScrollArea {{
            border: none;
            background-color: transparent;
        }}

        QScrollArea > QWidget > QWidget {{
            background-color: transparent;
        }}

        QScrollBar:vertical {{
            background-color: {ModernStyles.BACKGROUND_PRIMARY};
            width: 12px;
            border-radius: 6px;
            margin: 0px;
        }}

        QScrollBar::handle:vertical {{
            background-color: {ModernStyles.BORDER_COLOR};
            border-radius: 6px;
            min-height: 20px;
            margin: 2px;
        }}

        QScrollBar::handle:vertical:hover {{
            background-color: {ModernStyles.TEXT_SECONDARY};
        }}

        QScrollBar::handle:vertical:pressed {{
            background-color: {ModernStyles.PRIMARY_COLOR};
        }}

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}

        QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
            background: none;
        }}
        """
    
    @staticmethod
    def get_tab_widget_style():
        """Get tab widget stylesheet"""
        return f"""
        QTabWidget::pane {{
            border: 1px solid {ModernStyles.BORDER_COLOR};
            border-radius: 4px;
            background-color: {ModernStyles.BACKGROUND_SECONDARY};
        }}
        
        QTabBar::tab {{
            background-color: {ModernStyles.BACKGROUND_PRIMARY};
            color: {ModernStyles.TEXT_SECONDARY};
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {ModernStyles.PRIMARY_COLOR};
            color: {ModernStyles.TEXT_WHITE};
        }}
        
        QTabBar::tab:hover:!selected {{
            background-color: {ModernStyles.PRIMARY_LIGHT};
            color: {ModernStyles.TEXT_PRIMARY};
        }}
        """
    
    @staticmethod
    def get_status_label_style(status_type="normal"):
        """Get status label stylesheet"""
        if status_type == "connected":
            color = ModernStyles.SUCCESS_COLOR
        elif status_type == "error":
            color = ModernStyles.ERROR_COLOR
        elif status_type == "warning":
            color = ModernStyles.WARNING_COLOR
        else:
            color = ModernStyles.TEXT_SECONDARY
            
        return f"""
        QLabel {{
            color: {color};
            font-weight: bold;
            font-size: 9pt;
            padding: 4px 8px;
            border-radius: 4px;
            background-color: rgba({color[1:3]}, {color[3:5]}, {color[5:7]}, 0.1);
        }}
        """
    
    @staticmethod
    def get_tool_button_style():
        """Get tool button stylesheet"""
        return f"""
        QToolButton {{
            background-color: {ModernStyles.BACKGROUND_SECONDARY};
            border: 1px solid {ModernStyles.BORDER_COLOR};
            border-radius: 6px;
            color: {ModernStyles.TEXT_PRIMARY};
            font-weight: 500;
            font-size: 8pt;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            padding: 6px 10px;
            min-height: 20px;
            min-width: 28px;
        }}

        QToolButton:hover {{
            background-color: {ModernStyles.PRIMARY_LIGHT};
            border-color: {ModernStyles.PRIMARY_COLOR};
            border-width: 2px;
        }}

        QToolButton:pressed {{
            background-color: {ModernStyles.PRIMARY_COLOR};
            color: {ModernStyles.TEXT_WHITE};
            border-color: {ModernStyles.PRIMARY_DARK};
        }}

        QToolButton:disabled {{
            background-color: {ModernStyles.BACKGROUND_PRIMARY};
            color: {ModernStyles.TEXT_HINT};
            border-color: {ModernStyles.BORDER_COLOR};
        }}
        """
