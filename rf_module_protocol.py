"""
RF Module CAN Protocol Implementation - V3.0
Implements the user CAN protocol for calibration module
Based on Doc/CAN通信协议-用户.md
"""

import struct
import time
from datetime import datetime


class RFModuleProtocol:
    """RF Module protocol handler for calibration system"""
    
    # Message category
    MSG_CATEGORY = 0xF0
    
    # Message IDs
    MSG_ID_PRODUCT_INFO = 0x00     # 模块产品信息查询
    MSG_ID_BIT_QUERY = 0x01        # 模块BIT查询
    MSG_ID_CHANNEL_QUERY = 0x02    # 模块通道参数查询
    MSG_ID_CHANNEL_SET = 0x04      # 模块通道参数设置
    MSG_ID_RESET = 0x05            # 模块复位参数设置
    MSG_ID_INIT_STATUS = 0x2E      # 模块初始化状态结果
    
    # Channel definitions
    CHANNEL_ALL = 0x00      # 全通道
    CHANNEL_1 = 0x01        # 通道1 (低段30M-3G)
    CHANNEL_2 = 0x02        # 通道2 (高段2G-18G)
    
    # Work modes
    MODE_SINGLE_FREQ = 0    # 单频标校工作模式
    MODE_SWEEP_FREQ = 1     # 扫频标校工作模式(不支持)
    MODE_COMB_SPECTRUM = 2  # 梳状谱标校工作模式
    MODE_LOW_POWER = 3      # 低功耗工作模式
    
    # Signal control
    SIGNAL_OFF = 0          # 输出关闭
    SIGNAL_ON = 1           # 输出开启
    
    # Frequency ranges in kHz
    FREQ_CH1_MIN = 30000       # 30MHz
    FREQ_CH1_MAX = 3000000     # 3GHz
    FREQ_CH2_MIN = 2000000     # 2GHz
    FREQ_CH2_MAX = 18000000    # 18GHz
    
    def __init__(self, can_interface):
        self.can = can_interface
        self.log_callback = None
        self.status_log_callback = None
        
    def set_log_callback(self, callback):
        """Set log callback for debug output"""
        self.log_callback = callback
        
    def set_status_log_callback(self, callback):
        """Set status log callback for human-readable status display"""
        self.status_log_callback = callback
        
    def _log(self, message):
        """Log message using callback or print"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(f"[Protocol] {message}")
    
    def query_product_info(self):
        """Query module product information
        
        Returns:
            dict: Product information or None if failed
            {
                'manufacturer_code': int,
                'product_date': str,      # YYYY-MM-DD
                'serial_number': str,     # YYYY-BB-NNN
                'mark_address': int,
                'freq_range_low': int,    # kHz
                'freq_range_high': int    # kHz
            }
        """
        # Build query command
        frame = [self.MSG_CATEGORY, self.MSG_ID_PRODUCT_INFO]
        
        self._log("Sending product info query")
        hex_str = ' '.join(f'{b:02X}' for b in frame)
        self._log(f"TX Frame: {hex_str}")
        
        # Clear receive buffer
        if hasattr(self.can, 'clear_receive_buffer'):
            self.can.clear_receive_buffer()
        
        # Send query
        if not self.can.send_message(frame):
            self._log("Failed to send product info query")
            return None
        
        # Wait for response (21 bytes)
        response = self._wait_for_response(self.MSG_ID_PRODUCT_INFO, 21)
        if not response:
            return None
        
        # Parse response
        try:
            # Manufacturer code
            manufacturer_code = response[2]
            
            # Product date (BCD format)
            year = ((response[3] << 8) | response[4])
            month = response[5]
            day = response[6]
            product_date = f"{self._bcd_to_dec(year):04d}-{self._bcd_to_dec(month):02d}-{self._bcd_to_dec(day):02d}"
            
            # Serial number (BCD format)
            serial_year = ((response[7] << 8) | response[8])
            serial_batch = response[9]
            serial_num = ((response[10] & 0x0F) << 8) | ((response[11] >> 4) & 0x0F) * 10 | (response[11] & 0x0F)
            serial_number = f"{self._bcd_to_dec(serial_year):04d}-{self._bcd_to_dec(serial_batch):02d}-{self._bcd_to_dec(serial_num):03d}"
            
            # MARK address
            mark_address = response[12] & 0x7F
            
            # Frequency range
            freq_low = (response[13] << 24) | (response[14] << 16) | (response[15] << 8) | response[16]
            freq_high = (response[17] << 24) | (response[18] << 16) | (response[19] << 8) | response[20]
            
            info = {
                'manufacturer_code': manufacturer_code,
                'product_date': product_date,
                'serial_number': serial_number,
                'mark_address': mark_address,
                'freq_range_low': freq_low,
                'freq_range_high': freq_high
            }
            
            # Log to status
            if self.status_log_callback:
                status_msg = "产品信息:\n"
                status_msg += f"厂家代号: {manufacturer_code}\n"
                status_msg += f"出厂日期: {product_date}\n"
                status_msg += f"序列号: {serial_number}\n"
                status_msg += f"MARK地址: {mark_address}\n"
                status_msg += f"频率范围: {freq_low/1000:.0f}-{freq_high/1000:.0f}MHz"
                self.status_log_callback(status_msg)
            
            return info
            
        except Exception as e:
            self._log(f"Error parsing product info: {e}")
            return None
    
    def query_bit_info(self):
        """Query BIT (Built-In Test) information from module
        
        Returns:
            dict: BIT information or None if failed
            {
                'temperature': int,          # Temperature in Celsius
                'channel_voltages': list,    # 8 channel voltages (V)
                'neg_5v_voltage': float,     # -5V voltage
                'power_status': int,         # Bit field for power status
                'lo_lock_status': int,       # Bit field for LO lock
                'fpga_da_status': int        # FPGA/DA status
            }
        """
        # Build query command
        frame = [self.MSG_CATEGORY, self.MSG_ID_BIT_QUERY]
        
        self._log("Sending BIT query command")
        hex_str = ' '.join(f'{b:02X}' for b in frame)
        self._log(f"TX Frame: {hex_str}")
        
        # Clear receive buffer
        if hasattr(self.can, 'clear_receive_buffer'):
            self.can.clear_receive_buffer()
        
        # Send query
        if not self.can.send_message(frame):
            self._log("Failed to send BIT query")
            return None
        
        # Wait for response (15 bytes)
        response = self._wait_for_response(self.MSG_ID_BIT_QUERY, 15)
        if not response:
            return None
        
        # Parse response
        try:
            # Temperature (signed byte)
            temperature = struct.unpack('b', bytes([response[2]]))[0]
            
            # Channel voltages (8 channels, 0.1V units)
            channel_voltages = []
            for i in range(8):
                voltage_raw = response[3 + i]
                if voltage_raw == 0xFF:
                    channel_voltages.append(None)  # Invalid
                else:
                    channel_voltages.append(voltage_raw * 0.1)
            
            # -5V voltage
            neg_5v_raw = response[11]
            neg_5v_voltage = None if neg_5v_raw == 0xFF else neg_5v_raw * -0.1
            
            # Power status bits
            power_status = response[12]
            
            # LO lock status bits
            lo_lock_status = response[13]
            
            # FPGA/DA status
            fpga_da_status = response[14]
            
            bit_info = {
                'temperature': temperature,
                'channel_voltages': channel_voltages,
                'neg_5v_voltage': neg_5v_voltage,
                'power_status': power_status,
                'lo_lock_status': lo_lock_status,
                'fpga_da_status': fpga_da_status
            }
            
            # Log parsed values
            self._log(f"BIT Info: Temp={temperature}°C, Power status=0x{power_status:02X}, "
                     f"LO lock=0x{lo_lock_status:02X}, FPGA/DA=0x{fpga_da_status:02X}")
            
            # Log to status
            if self.status_log_callback:
                status_msg = "BIT信息:\n"
                status_msg += f"温度: {temperature}°C\n"
                
                # Show voltages for active channels (1 and 2)
                if channel_voltages[0] is not None:
                    status_msg += f"通道1电压: {channel_voltages[0]:.1f}V\n"
                if channel_voltages[1] is not None:
                    status_msg += f"通道2电压: {channel_voltages[1]:.1f}V\n"
                
                # Power and lock status
                status_msg += f"通道1功率: {'正常' if power_status & 0x01 else '异常'}\n"
                status_msg += f"通道2功率: {'正常' if power_status & 0x02 else '异常'}\n"
                status_msg += f"本振1锁定: {'是' if lo_lock_status & 0x01 else '否'}\n"
                status_msg += f"本振2锁定: {'是' if lo_lock_status & 0x02 else '否'}\n"
                status_msg += f"FPGA状态: {'正常' if fpga_da_status == 0xFF else '异常'}"
                self.status_log_callback(status_msg)
            
            return bit_info
            
        except Exception as e:
            self._log(f"Error parsing BIT response: {e}")
            return None
    
    def query_channel_params(self):
        """Query all channel parameters
        
        Returns:
            dict: Channel parameters or None if failed
            {
                'channel_1': {
                    'attenuation': int,
                    'frequency': int,      # kHz
                    'signal_switch': int,
                    'work_mode': int
                },
                'channel_2': {
                    'attenuation': int,
                    'frequency': int,      # kHz  
                    'signal_switch': int,
                    'work_mode': int
                }
            }
        """
        # Build query command (only supports all channel query)
        frame = [self.MSG_CATEGORY, self.MSG_ID_CHANNEL_QUERY, self.CHANNEL_ALL]
        
        self._log("Sending channel parameters query")
        hex_str = ' '.join(f'{b:02X}' for b in frame)
        self._log(f"TX Frame: {hex_str}")
        
        # Clear receive buffer
        if hasattr(self.can, 'clear_receive_buffer'):
            self.can.clear_receive_buffer()
        
        # Send query
        if not self.can.send_message(frame):
            self._log("Failed to send channel params query")
            return None
        
        # Wait for response (18 bytes)
        response = self._wait_for_response(self.MSG_ID_CHANNEL_QUERY, 18)
        if not response:
            return None
        
        # Parse response
        try:
            params = {}
            
            # Channel 1 parameters
            ch1_atten = response[3]
            ch1_freq = (response[4] << 24) | (response[5] << 16) | (response[6] << 8) | response[7]
            ch1_status = response[8]
            ch1_switch = (ch1_status >> 1) & 0x01
            ch1_mode = ch1_status & 0x03
            
            params['channel_1'] = {
                'attenuation': ch1_atten,
                'frequency': ch1_freq,
                'signal_switch': ch1_switch,
                'work_mode': ch1_mode
            }
            
            # Channel 2 parameters
            ch2_atten = response[9]
            ch2_freq = (response[10] << 24) | (response[11] << 16) | (response[12] << 8) | response[13]
            ch2_status = response[14]
            ch2_switch = (ch2_status >> 1) & 0x01
            ch2_mode = ch2_status & 0x03
            
            params['channel_2'] = {
                'attenuation': ch2_atten,
                'frequency': ch2_freq,
                'signal_switch': ch2_switch,
                'work_mode': ch2_mode
            }
            
            # Log to status
            if self.status_log_callback:
                status_msg = "通道参数:\n"
                status_msg += f"通道1: {ch1_freq/1000:.0f}MHz, 衰减{ch1_atten}dB, "
                status_msg += f"{'开启' if ch1_switch else '关闭'}, "
                status_msg += f"模式{ch1_mode}\n"
                status_msg += f"通道2: {ch2_freq/1000:.0f}MHz, 衰减{ch2_atten}dB, "
                status_msg += f"{'开启' if ch2_switch else '关闭'}, "
                status_msg += f"模式{ch2_mode}"
                self.status_log_callback(status_msg)
            
            return params
            
        except Exception as e:
            self._log(f"Error parsing channel params response: {e}")
            return None
        
    def set_channel_params(self, channel, params):
        """Set parameters for specific channel
        
        Args:
            channel: CHANNEL_1 or CHANNEL_2
            params: dict with keys:
                - signal_switch: 0 (off) or 1 (on)
                - work_mode: 0-3 (see MODE_* constants)
                - frequency: frequency in kHz
                - attenuation: attenuation in dB (0-50, 5dB steps)
            
        Returns:
            bool: True if successful
        """
        # Validate channel
        if channel not in [self.CHANNEL_1, self.CHANNEL_2]:
            self._log(f"Invalid channel: {channel}")
            return False
        
        # Validate parameters
        if not self._validate_channel_params(channel, params):
            return False
        
        # Log the human-readable parameters
        if self.status_log_callback:
            ch_name = "通道1" if channel == self.CHANNEL_1 else "通道2"
            status_msg = f"设置{ch_name}参数:\n"
            status_msg += f"频率: {params['frequency']/1000:.0f}MHz\n"
            status_msg += f"衰减: {params['attenuation']}dB\n"
            status_msg += f"信号: {'开启' if params['signal_switch'] else '关闭'}\n"
            status_msg += f"模式: {self._get_mode_name(params['work_mode'])}"
            self.status_log_callback(status_msg)
        
        # Build parameter setting frame (19 bytes)
        frame = [0] * 19
        
        # Header
        frame[0] = self.MSG_CATEGORY
        frame[1] = self.MSG_ID_CHANNEL_SET
        frame[2] = channel  # Channel number
        frame[3] = params['work_mode'] & 0x03  # Work mode
        
        # Channel switches (bit field)
        frame[4] = 0x00
        if channel == self.CHANNEL_1:
            frame[4] |= (params['signal_switch'] & 0x01)
        else:  # CHANNEL_2
            frame[4] |= ((params['signal_switch'] & 0x01) << 1)
        
        # Center frequency (big-endian, kHz)
        freq = int(params['frequency'])
        frame[5] = (freq >> 24) & 0xFF
        frame[6] = (freq >> 16) & 0xFF
        frame[7] = (freq >> 8) & 0xFF
        frame[8] = freq & 0xFF
        
        # Reserved bytes 9-17
        for i in range(9, 18):
            frame[i] = 0xFF
        
        # Attenuation
        frame[18] = int(params['attenuation']) & 0xFF
        
        # Log the raw hex data
        hex_str = ' '.join(f'{b:02X}' for b in frame)
        self._log(f"TX Frame: {hex_str}")
        
        # Clear receive buffer
        if hasattr(self.can, 'clear_receive_buffer'):
            self.can.clear_receive_buffer()
        
        # Send frame using multi-frame method
        if not self.can.send_multi_frame(frame):
            self._log("Failed to send parameter frame")
            return False
        
        # Wait for confirmation (4 bytes)
        response = self._wait_for_response(self.MSG_ID_CHANNEL_SET, 4, timeout=1.0)
        if response:
            # Check response
            if len(response) >= 4 and response[2] == channel:
                result = response[3]
                if result == 0x01:
                    self._log("Parameter setting confirmed")
                    return True
                else:
                    self._log("Parameter setting failed (device rejected)")
                    return False
        
        # If no response, assume success (some devices don't respond)
        self._log("No confirmation received, assuming success")
        return True
    
    def send_reset(self):
        """Send module reset command
        
        Returns:
            bool: True if successful
        """
        # Build reset command
        frame = [self.MSG_CATEGORY, self.MSG_ID_RESET, 0x01]
        
        self._log("Sending reset command")
        hex_str = ' '.join(f'{b:02X}' for b in frame)
        self._log(f"TX Frame: {hex_str}")
        
        # Clear receive buffer
        if hasattr(self.can, 'clear_receive_buffer'):
            self.can.clear_receive_buffer()
        
        # Send command
        if not self.can.send_message(frame):
            self._log("Failed to send reset command")
            return False
        
        self._log("Reset command sent")
        
        # Log to status
        if self.status_log_callback:
            self.status_log_callback("发送模块复位命令")
        
        return True
    
    def _validate_channel_params(self, channel, params):
        """Validate channel parameters"""
        # Get frequency range for channel
        if channel == self.CHANNEL_1:
            freq_min, freq_max = self.FREQ_CH1_MIN, self.FREQ_CH1_MAX
            ch_name = "通道1"
        else:
            freq_min, freq_max = self.FREQ_CH2_MIN, self.FREQ_CH2_MAX
            ch_name = "通道2"
        
        # Frequency range check
        freq = params['frequency']
        if not (freq_min <= freq <= freq_max):
            self._log(f"Invalid {ch_name} frequency: {freq/1000:.0f}MHz (range: {freq_min/1000:.0f}-{freq_max/1000:.0f}MHz)")
            return False
            
        # Attenuation: 0-50 dB, 5dB steps
        att = params['attenuation']
        if att % 5 != 0 or not (0 <= att <= 50):
            self._log(f"Invalid {ch_name} attenuation: {att}dB (0-50, 5dB steps)")
            return False
            
        # Signal switch: 0 or 1
        if params['signal_switch'] not in [0, 1]:
            self._log(f"Invalid {ch_name} signal switch: {params['signal_switch']}")
            return False
            
        # Work mode: 0-3
        if params['work_mode'] not in [0, 1, 2, 3]:
            self._log(f"Invalid {ch_name} work mode: {params['work_mode']}")
            return False
            
        return True
    
    def _wait_for_response(self, expected_msg_id, expected_length, timeout=2.0):
        """Wait for specific response message
        
        Args:
            expected_msg_id: Expected message ID
            expected_length: Expected total response length
            timeout: Timeout in seconds
            
        Returns:
            list: Complete response data or None
        """
        start_time = time.time()
        received_data = []
        frame_count = 0
        
        # Add initial delay for MCU processing
        time.sleep(0.15)
        
        while time.time() - start_time < timeout:
            # Try to receive messages
            messages = self.can.receive_messages(max_messages=10, timeout=100)
            
            for msg in messages:
                # Log received message
                hex_str = ' '.join(f'{b:02X}' for b in msg.data)
                self._log(f"RX Frame: {hex_str}")
                
                # Check if this is the expected response start
                if len(received_data) == 0 and len(msg.data) >= 2:
                    if msg.data[0] == self.MSG_CATEGORY and msg.data[1] == expected_msg_id:
                        received_data.extend(msg.data)
                        frame_count += 1
                        self._log(f"Response frame {frame_count} (start)")
                elif len(received_data) > 0 and len(received_data) < expected_length:
                    # Continue collecting multi-frame data
                    received_data.extend(msg.data)
                    frame_count += 1
                    self._log(f"Response frame {frame_count} (continuation)")
                    
                    # Check if we have complete response
                    if len(received_data) >= expected_length:
                        self._log(f"Complete response received: {len(received_data)} bytes")
                        return received_data[:expected_length]
            
            # Small delay between receive attempts
            if len(messages) == 0:
                time.sleep(0.02)
        
        self._log(f"Response timeout - received {len(received_data)} bytes in {frame_count} frames")
        return None
    
    def _bcd_to_dec(self, bcd):
        """Convert BCD to decimal"""
        result = 0
        multiplier = 1
        while bcd > 0:
            digit = bcd & 0x0F
            result += digit * multiplier
            multiplier *= 10
            bcd >>= 4
        return result
    
    def _get_mode_name(self, mode):
        """Get work mode name"""
        mode_names = {
            0: "单频标校",
            1: "扫频标校(不支持)",
            2: "梳状谱标校",
            3: "低功耗"
        }
        return mode_names.get(mode, f"未知模式({mode})")
    
    def get_frequency_range(self, channel):
        """Get frequency range for specified channel
        
        Args:
            channel: CHANNEL_1 or CHANNEL_2
            
        Returns:
            tuple: (min_freq, max_freq) in MHz
        """
        if channel == self.CHANNEL_1:
            return (self.FREQ_CH1_MIN / 1000, self.FREQ_CH1_MAX / 1000)
        elif channel == self.CHANNEL_2:
            return (self.FREQ_CH2_MIN / 1000, self.FREQ_CH2_MAX / 1000)
        else:
            raise ValueError(f"Invalid channel: {channel}")

    def get_attenuation_range(self):
        """Get attenuation range for both channels
        
        Returns:
            tuple: (min_att, max_att, step) in dB
        """
        return (0, 50, 5)